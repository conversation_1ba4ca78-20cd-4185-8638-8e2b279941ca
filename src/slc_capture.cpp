#include "ipc/ipc.h"
#include <string>
#include <chrono>
#include <thread>

#ifdef __cplusplus
extern "C" {
#endif
#include <pthread.h>
#include <pcap.h>  // for libpcap/WinPcap calls
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <sys/stat.h>
#include <unistd.h>

#define CHECK_NOR_EXIT(cond, errMsg, retCode) \
  if ((cond)) {                               \
    fprintf(stderr, "%s\n", errMsg);          \
    return (retCode);                         \
  }
void mkdirs(const char *muldir) {
  if (NULL == muldir) {
    return;
  }
  if (access(muldir, 0) == 0) {
    return;
  }
  int  i, len;
  char str[512];

  strncpy(str, muldir, 512);
  len = strlen(str);
  for (i = 0; i < len; i++) {
    if (str[i] == '/') {
      str[i] = '\0';
      if (access(str, 0) != 0) {
        mkdir(str, 0777);
      }
      str[i] = '/';
    }
  }
  if (len > 0 && access(str, 0) != 0) {
    mkdir(str, 0777);
  }

  return;
}
#ifdef __cplusplus
}
#endif
#include "slc_capture.h"
#ifndef SOCKET
#define SOCKET int
#endif

static int on_new_stream_callback(uint64_t stream_id, void *userdata) {
  auto cap = static_cast<SlcCap *>(userdata);

  uint64_t          ret = 0;
  struct context_t *context = (struct context_t *)userdata;

  // printf("on_new_stream_callback = %lu\n", stream_id);

  // 通过插件接口调用函数
  if (!cap->plugin_loader_ || !cap->plugin_loader_->is_loaded()) {
    printf("Plugin not loaded, skipping stream capability check\n");
    return 0;
  }

  const struct plugin_interface* interface = cap->plugin_loader_->get_interface();
  if (!interface) {
    printf("Plugin interface not available\n");
    return 0;
  }

  ret = interface->ipc_deocder_get_stream_capability(cap->decoder_handle, stream_id);

  if (ret & IPC_CAPABILITY_ADVANCED_VIDEO_CODING) {
    int i = interface->ipc_deocder_get_advanced_video_coding(cap->decoder_handle, stream_id);
    // printf("advanced_video_coding = %d\n", i);
  }

  if (ret & IPC_CAPABILITY_ADVANCED_AUDIO_CODING) {
    int i = interface->ipc_deocder_get_advanced_audio_coding(cap->decoder_handle, stream_id);
    // printf("advanced_audio_coding = %d\n", i);
  }

  if (ret & IPC_CAPABILITY_DEVICEID) {
    uint8_t buff[1024] = {0};
    interface->ipc_deocder_get_deviceid(cap->decoder_handle, stream_id, buff, sizeof(buff));
    // printf("devideid = %s\n", buff);
  }

  IPC_PROTOCOL_TYPE type = IPC_PROTOCOL_UNKNOWN;
  if (ret & IPC_CAPABILITY_IPC_PROTO_TYPE) {
    type = interface->ipc_deocder_get_ipc_protocol(cap->decoder_handle, stream_id);
    // printf("type = %d\n", type);
  }
  if (ret & IPC_CAPABILITY_SAMPLE_RATE) {
    int rate = interface->ipc_deocder_get_stream_sample_rate(cap->decoder_handle, stream_id);
    // printf("rate = %d\n", rate);
  }
  yaslc_status_t status;
  status.type = type;
  if (type == IPC_PROTOCOL_UNKNOWN) {
    return 0;
  } else {
    status.status = YASLC_YES;
  }

  // 通过Unix Socket发送状态更新到客户端
  cap->sendStatusToClients(status);

  return 0;
}
static int on_new_nalu_callback(
    uint64_t stream_id, uint64_t seq, struct ipc_nalu_t *nalu_head, uint64_t nalu_len, void *userdata) {
  // printf("on_new_nalu_callback len %llu\n", nalu_len);
  auto cap = static_cast<SlcCap *>(userdata);
  if (cap->f != NULL) {
    fwrite("\x00\x00\x00\x01", 4, 1, cap->f);
    fwrite((uint8_t *)nalu_head, nalu_len, 1, cap->f);
    fflush(cap->f);
  }
  cap->sendNaluDataToClients((const uint8_t*)nalu_head,nalu_len);

  return 0;
}

struct thread_session {
  SOCKET       sockctrl;
  SOCKET       sockdata;
  uint8_t      protocol_version;
  unsigned int TotCapt;
  int          have_thread;
  pthread_t    pthread;
  pcap_t      *handle;  //libpcap句柄
};

struct thread_session flow_thread_info;

SlcCap::SlcCap(const std::string &strSlcCapFilter,
               const std::string &plugin_path, const std::string &filepath_,
               const std::string &nalu_socket_path,
               const std::string &control_socket_path)
    : strSlcCapFilter_(strSlcCapFilter), plugin_loader_(nullptr),
      decoder_handle(nullptr), filepath_(filepath_), nalu_server_(nullptr),
      nalu_socket_path_(nalu_socket_path), control_client_(nullptr),
      control_socket_path_(control_socket_path), is_capturing_(false),
      heartbeat_running_(false) {

  // 创建插件加载器
  plugin_loader_ = new PluginLoader();

  // 确定插件路径
  std::string actual_plugin_path = plugin_path;
  if (actual_plugin_path.empty()) {
    // 如果没有指定插件路径，使用默认路径
    actual_plugin_path = "./libipc.so";
  }

  // 加载插件
  if (plugin_loader_->load_plugin(actual_plugin_path) != 0) {
    std::cerr << "Failed to load plugin: " << actual_plugin_path << std::endl;
    return;
  }

  const struct plugin_interface* iface = plugin_loader_->get_interface();
  if (!iface) {
    std::cerr << "Failed to get plugin interface" << std::endl;
    return;
  }

  //创建config句柄
  IPC_CONFIG *config_handle = iface->ipc_create_config(on_new_stream_callback, on_new_nalu_callback);

  // 设置上下文
  iface->ipc_cfg_set_user_data(config_handle, this);

  //设置捕获网络类型
  IPC_FRAME_TYPE_E type = (IPC_FRAME_TYPE_E)ARM_DEVICE;
  iface->ipc_cfg_set_config_frame_type(config_handle, type);

  //设置排除的网络报文ip
  const char *pStrValue = RTSP_SERVER_IP;
  uint32_t    excludeIP_ = ntohl(inet_addr(pStrValue));
  iface->ipc_cfg_set_exclude_packet_ip(config_handle, excludeIP_);

  //设置日志等级
  int log_level = LOG_LEVEL;
  iface->ipc_cfg_set_config_log_level(config_handle, (enum IPC_LOG_LEVEL_E)log_level);

  //创建decoder句柄
  decoder_handle = iface->ipc_create_decoder(config_handle);


  // 从配置文件读取心跳间隔
  heartbeat_interval_ = SlcConfig::GetInstance()->GetValueOf<int>("HEARTBEAT_INTERVAL", 5);

  // 初始化NALU Socket服务器
  startNaluSocketServer();

  // 初始化控制Socket客户端
  startControlClient();

  // 启动心跳
  startHeartbeat();
}

SlcCap::~SlcCap() {
  if (plugin_loader_) {
    if (decoder_handle && plugin_loader_->is_loaded()) {
      const struct plugin_interface* iface = plugin_loader_->get_interface();
      if (iface && iface->ipc_destroy_decoder) {
        iface->ipc_destroy_decoder(decoder_handle);
      }
    }
    delete plugin_loader_;
    plugin_loader_ = nullptr;
  }
  decoder_handle = nullptr;


  // 停止心跳
  stopHeartbeat();

  // 停止NALU Socket服务器
  stopNaluSocketServer();

  // 停止控制Socket客户端
  stopControlClient();

}

void SlcCap::onGotLivePkt(uint8_t *user, const struct pcap_pkthdr *h, const u_char *bytes) {
  if (NULL == user) {
    return;
  }
  ((SlcCap *)user)->parsePcapPkt(&h->ts, 0, h->caplen, bytes);
}

std::string SlcCap::GenerateCapFileName() {
  static char    time_str[64];
  struct tm      tm_tmp;
  struct timeval tv;
  gettimeofday(&tv, NULL);

  localtime_r(&tv.tv_sec, &tm_tmp);
  strftime(time_str, sizeof(time_str), "%Y%m%d%H%M%S", &tm_tmp);

  std::string filename = filepath_ + "/" + brand_ + "_" + model_ + "_" + time_str + ".pcap";
  return filename;
}

int SlcCap::parseSlcCapture() {
  // 从config.ini读取默认配置
  SlcConfig* config = SlcConfig::GetInstance();
  if (!config) {
    fprintf(stderr, "Failed to get config instance\n");
    return -1;
  }

  // 从配置文件读取默认值
  std::string default_brand = config->GetValueOf<std::string>("DEFAULT_BRAND", "Unknown");
  std::string default_model = config->GetValueOf<std::string>("DEFAULT_MODEL", "Unknown");
  int defultCapMaxFileSize_ = config->GetValueOf<int>("CAPTURE_FILE_SIZE", 100);
  filepath_ = filepath_.size()
                  ? filepath_
                  : config->GetValueOf<std::string>("CAPTURE_PATH", "./pcaps/");
  capMaxFileSize_ =
      capMaxFileSize_ ? capMaxFileSize_ : defultCapMaxFileSize_ * 1024 * 1024;
  // 如果没有从消息中设置过，使用默认值
  if (brand_.empty()) brand_ = default_brand;
  if (model_.empty()) model_ = default_model;

  // 确保目录存在
  if (access(filepath_.c_str(), 0) != 0) {
    mkdirs(filepath_.c_str());
  }
  capMaxFileSize_ = capMaxFileSize_ * 1024 * 1024;

  setCapFileName(GenerateCapFileName().c_str());

  return 0;
}

void SlcCap::clearCapInfo() {
  brand_.clear();
  model_.clear();
  capFileName.clear();
  capFileNameFinish.clear();
  capFileSize_ = 0;
  capFileFp = NULL;
}

void SlcCap::setCapFileSwitch(int cap_switch) {
  if (cap_switch == cap_switch_) {
    //重复动作
    return;
  } else {
    if (cap_switch) {
      parseSlcCapture();
    } else {
      saveFileStop();
    }
    cap_switch_ = cap_switch;
  }
}

int SlcCap::saveFileStart() {
  if (capFileName.size() == 0) {
    GenerateCapFileName();
  }
  if (capFileFp == NULL) {
    struct stat st;
    capFileFp = fopen(capFileName.c_str(), "w");
    if (capFileFp) {
      struct pcap_file_header fh;
      memset(&fh, 0, sizeof(struct pcap_file_header));
      fh.magic = 0xA1B2C3D4;
      fh.version_major = PCAP_VERSION_MAJOR;
      fh.version_minor = PCAP_VERSION_MINOR;
      fh.linktype = DLT_EN10MB;
      fh.snaplen = 0XFFFFFFFF;
      fwrite(&fh, sizeof(fh), 1, capFileFp);
    } else {
      return -1;
    }
  }
  return 1;
}

void SlcCap::saveFileStop() {
  if (capFileFp) {
    fclose(capFileFp);
    rename(capFileName.c_str(),capFileNameFinish.c_str());
    clearCapInfo();
    setCapFileSwitch(0);
  }
  parseSlcCapture();
}

void SlcCap::saveFile(const uint8_t *pPktData, int len) {
  int ret = 0;
  if(capFileSize_ == 0 || capFileFp == NULL){
    ret = saveFileStart();
  }
  if (capFileSize_ > capMaxFileSize_) {
    //文件开始或轮换
    saveFileStop();
    // ret = saveFileStart();
  }
  if (ret == -1 ||capFileFp == NULL) {
    return;
  }
  struct pcappkt_hdr pckt_header;
  int                wcnt = 0, nwrt = 0;
  struct timeval     tv;
  gettimeofday(&tv, NULL);
  pckt_header.caplen = len;
  pckt_header.len = len;
  pckt_header.tv_sec = tv.tv_sec;
  pckt_header.tv_usec = tv.tv_usec;

  //写pcap头
  do {
    nwrt = fwrite(((char *)&pckt_header) + wcnt, 1, sizeof(struct pcappkt_hdr) - wcnt, capFileFp);
    fflush(capFileFp);
    if (nwrt > 0)
      wcnt += nwrt;
    else
      break;
  } while (wcnt != sizeof(struct pcappkt_hdr));

  wcnt = 0;
  do {
    nwrt = fwrite(pPktData + wcnt, 1, len - wcnt, capFileFp);
    fflush(capFileFp);
    if (nwrt > 0)
      wcnt += nwrt;
    else
      break;
  } while (wcnt != len);
  capFileSize_ = ftell(capFileFp);
}

void SlcCap::parsePcapPkt(const timeval *pCapTs, int pktNO, int len, const uint8_t *pPktData) {
  if (plugin_loader_ && plugin_loader_->is_loaded() && decoder_handle) {
    const struct plugin_interface* iface = plugin_loader_->get_interface();
    if (iface && iface->ipc_process_packet) {
      iface->ipc_process_packet(decoder_handle, pPktData, len);
    }
  }
  if (cap_switch_) {
    saveFile(pPktData, len);
  }
}

int SlcCap::inspectLiveCap(const char *pStrIf) {
  bpf_program fp;
  char        errbuf[PCAP_ERRBUF_SIZE] = {0};
  char        filter_exp[PCAP_ERRBUF_SIZE] = {0};
  pcap_t     *pcapHandle = NULL;
  int         lSts = 0, count = 0;

  strncpy(filter_exp, strSlcCapFilter_.c_str(), sizeof filter_exp);
  /* Open the session in promiscuous mode */
  int bufsize = BUFSIZ;
  pcapHandle = pcap_open_live(pStrIf, 2000, 1, -1, errbuf);
  CHECK_NOR_EXIT(NULL == pcapHandle, "Couldn't open if.\n", -1);

  // loop
  pcap_loop(pcapHandle, 0, SlcCap::onGotLivePkt, (u_char *)this);

  pcap_close(pcapHandle);
  return 0;
}

int SlcCap::inspectPcapFile(const char *pPcapFileName) {
  bpf_program fp;
  char        errbuf[PCAP_ERRBUF_SIZE] = {0};
  char        filter_exp[PCAP_ERRBUF_SIZE] = {0};
  pcap_t     *pcapHandle = NULL;
  int         lSts = 0;

  strncpy(filter_exp, strSlcCapFilter_.c_str(), sizeof filter_exp);

  pcap_pkthdr   *pkt_header = NULL;
  const uint8_t *pkt_data = NULL;

  // 打开 pcap 文件
  pcapHandle = pcap_open_offline(pPcapFileName, errbuf);
  CHECK_NOR_EXIT(NULL == pcapHandle, errbuf, -1);

  /* pcap 过滤条件 */
  lSts = pcap_compile(pcapHandle, &fp, filter_exp, 0, 0);
  CHECK_NOR_EXIT(-1 == lSts, "Couldn't parse filter", -1);
  lSts = pcap_setfilter(pcapHandle, &fp);
  CHECK_NOR_EXIT(-1 == lSts, "Counldn't install filter", -1);

  // go ...
  printf("process pcap %s ... ", pPcapFileName);
  fflush(stdout);

  // process pkts
  for (int i = 1;; i++) {
    lSts = pcap_next_ex(pcapHandle, &pkt_header, &pkt_data);
    if (0 == lSts || 1 == lSts) {  // OK, parse pkt
      parsePcapPkt(&pkt_header->ts, i, pkt_header->caplen, pkt_data);
    } else if (-1 == lSts) {  // error
      fprintf(stderr, "%s\n", pcap_geterr(pcapHandle));
      break;
    } else if (-2 == lSts) {  // no more pkt to read from offline file
      printf("done.\n");
      break;
    }
  }

  pcap_freecode(&fp);
  pcap_close(pcapHandle);
  return 0;
}


void SlcCap::sendNaluDataToClients(const uint8_t* nalu_data, uint32_t length) {
    // 通过NALU Socket发送NALU数据到客户端
  if (nalu_server_) {
    nalu_server_->broadcast_message(IPC_MSG_STREAM_NALU, (const uint8_t*)nalu_data, length);
  }
}

void SlcCap::sendStatusToClients(const yaslc_status_t& status) {
  if (nalu_server_ && status.status == YASLC_YES) {
    // 当检测到新流时，发送IPC_MSG_STREAM_DETECTED消息
    sendStreamDetectedMessage(status.type);
  }
}

void SlcCap::sendHelloMessage() {
  if (control_client_) {
    // 创建HELLO消息体
    ipc_msg_hello hello_body;
    memset(&hello_body, 0, sizeof(hello_body));

    hello_body.dpi_version = 24;  // v2.4
    hello_body.plugin_version = 1;
    strncpy(hello_body.plugin_path, "./libipc.so", sizeof(hello_body.plugin_path) - 1);

    // 发送HELLO消息
    control_client_->send_message(IPC_MSG_HELLO, (const uint8_t*)&hello_body, sizeof(hello_body));
    std::cout << "Sent HELLO message to control server" << std::endl;
  }
}

void SlcCap::sendStreamDetectedMessage(IPC_PROTOCOL_TYPE proto_type) {
  if (control_client_) {
    // 创建STREAM_DETECTED消息体
    ipc_msg_stream_detected stream_body;
    memset(&stream_body, 0, sizeof(stream_body));

    stream_body.streamID = 1;  // 简化为固定流ID
    stream_body.flag = 0;      // 预留字段

    // 根据协议类型设置协议名称
    switch (proto_type) {
      case IPC_PROTOCOL_SIP:
        strncpy(stream_body.proto, "sip/rtp", sizeof(stream_body.proto) - 1);
        break;
      case IPC_PROTOCOL_DHAV:
        strncpy(stream_body.proto, "dahua", sizeof(stream_body.proto) - 1);
        break;
      case IPC_PROTOCOL_RTSP:
        strncpy(stream_body.proto, "rtsp/rtp", sizeof(stream_body.proto) - 1);
        break;
      case IPC_PROTOCOL_RTP:
        strncpy(stream_body.proto, "rtp", sizeof(stream_body.proto) - 1);
        break;
      default:
        strncpy(stream_body.proto, "unknown", sizeof(stream_body.proto) - 1);
        break;
    }

    // 发送STREAM_DETECTED消息
    control_client_->send_message(IPC_MSG_STREAM_DETECTED, (const uint8_t*)&stream_body, sizeof(stream_body));
    std::cout << "Sent STREAM_DETECTED message: protocol=" << stream_body.proto << ", streamID=" << stream_body.streamID << std::endl;
  }
}

// NALU Socket服务器相关方法
void SlcCap::startNaluSocketServer() {
  if (nalu_server_) {
    return; // 已经启动
  }

  nalu_server_ = new UnixSocketServerDgram(nalu_socket_path_);

  // 设置消息处理回调
  nalu_server_->set_message_handler([this](uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd) {
    // NALU服务器主要用于发送数据，不需要处理太多客户端消息
    switch (msg_type) {
      case IPC_MSG_HEART_BEAT:
        // 回复心跳
        nalu_server_->send_message(client_fd, IPC_MSG_HEART_BEAT, nullptr, 0);
        break;
      default:
        std::cout << "NALU server received unknown message type: " << msg_type << std::endl;
        break;
    }
  });

  if (!nalu_server_->start()) {
    std::cerr << "Failed to start NALU Socket server" << std::endl;
    delete nalu_server_;
    nalu_server_ = nullptr;
  } else {
    std::cout << "NALU Socket server started on " << nalu_socket_path_ << std::endl;
  }
}

void SlcCap::stopNaluSocketServer() {
  if (nalu_server_) {
    nalu_server_->stop();
    delete nalu_server_;
    nalu_server_ = nullptr;
    std::cout << "NALU Socket server stopped" << std::endl;
  }
}

// 控制Socket客户端相关方法
void SlcCap::startControlClient() {
  if (control_client_) {
    return; // 已经启动
  }

  control_client_ = new UnixSocketClientStream(control_socket_path_);

  // 设置消息处理回调
  control_client_->set_message_handler([this](uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd) {
    switch (msg_type) {
      case IPC_MSG_DUMP_START:
        if (length >= sizeof(ipc_msg_dump_start)) {
          const ipc_msg_dump_start* msg = reinterpret_cast<const ipc_msg_dump_start*>(data);
          std::string brand(msg->brand, strnlen(msg->brand, sizeof(msg->brand)));
          std::string serial(msg->serial, strnlen(msg->serial, sizeof(msg->serial)));
          std::cout << "Received dump start - Brand: " << brand << ", Serial: " << serial << ", Size: " << msg->dump_size << " bytes" << std::endl;
          // 将字节转换为MB
          uint32_t size_mb = msg->dump_size / (1024 * 1024);
          startCaptureWithInfo(brand, serial, size_mb);
        } else {
          std::cout << "Invalid dump start message length: " << length << std::endl;
        }
        break;
      case IPC_MSG_DUMP_STOP:
        std::cout << "Received dump stop command" << std::endl;
        stopCapture();
        break;
      case IPC_MSG_HEART_BEAT:
        // 心跳消息，不需要特殊处理
        break;
      default:
        std::cout << "Control client received unknown message type: " << msg_type << std::endl;
        break;
    }
  });

  // 尝试连接到控制服务器
  if (!control_client_->connect()) {
    std::cerr << "Failed to connect to control socket: " << control_socket_path_ << std::endl;
    delete control_client_;
    control_client_ = nullptr;
  } else {
    std::cout << "Connected to control socket: " << control_socket_path_ << std::endl;
    control_client_->start_async_receive();

    // 发送HELLO消息
    sendHelloMessage();
  }
}

void SlcCap::stopControlClient() {
  if (control_client_) {
    control_client_->stop_async_receive();
    control_client_->disconnect();
    delete control_client_;
    control_client_ = nullptr;
    std::cout << "Control client stopped" << std::endl;
  }
}

// 抓包控制相关方法
void SlcCap::startCapture() {
  std::lock_guard<std::mutex> lock(capture_mutex_);
  if (!is_capturing_) {
    is_capturing_ = true;
    std::cout << "Capture started" << std::endl;
    setCapFileSwitch(1);
    sendCaptureStatus(true);
  }
}

void SlcCap::stopCapture() {
  std::lock_guard<std::mutex> lock(capture_mutex_);
  if (is_capturing_) {
    is_capturing_ = false;
    std::cout << "Capture stopped" << std::endl;
    setCapFileSwitch(0);
    sendCaptureStatus(false);
  }
}

void SlcCap::startCaptureWithInfo(const std::string& brand, const std::string& model, uint32_t file_size_mb) {
  std::lock_guard<std::mutex> lock(capture_mutex_);
  if (!is_capturing_) {
    // 设置从消息中获取的信息
    brand_ = brand.empty() ? "Unknown" : brand;
    model_ = model.empty() ? "Unknown" : model;

    if (file_size_mb > 0) {
      capMaxFileSize_ = file_size_mb * 1024 * 1024;
    }

    is_capturing_ = true;
    std::cout << "Capture started with info - Brand: " << brand_
              << ", Model: " << model_
              << ", FileSize: " << file_size_mb << "MB" << std::endl;
    setCapFileSwitch(1);
    sendCaptureStatus(true);
  }
}

void SlcCap::sendCaptureStatus(bool capturing) {
  // 向控制服务器发送抓包状态
  if (control_client_) {
    if (capturing) {
      control_client_->send_message(IPC_MSG_DUMP_START, nullptr, 0);
    } else {
      control_client_->send_message(IPC_MSG_DUMP_DONE, nullptr, 0);
    }
  }
}

// 心跳相关方法实现
void SlcCap::startHeartbeat() {
  if (heartbeat_running_) {
    return; // 已经在运行
  }

  heartbeat_running_ = true;
  heartbeat_thread_ = std::thread([this]() {
    while (heartbeat_running_) {
      // 等待心跳间隔
      for (int i = 0; i < heartbeat_interval_ && heartbeat_running_; ++i) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }

      // 发送心跳
      if (heartbeat_running_) {
        sendHeartbeat();
      }
    }
  });

  std::cout << "Heartbeat started with interval: " << heartbeat_interval_ << " seconds" << std::endl;
}

void SlcCap::stopHeartbeat() {
  if (!heartbeat_running_) {
    return; // 已经停止
  }

  heartbeat_running_ = false;

  if (heartbeat_thread_.joinable()) {
    heartbeat_thread_.join();
  }

  std::cout << "Heartbeat stopped" << std::endl;
}

void SlcCap::sendHeartbeat() {
  // 向控制服务器发送心跳消息
  if (control_client_) {
    control_client_->send_message(IPC_MSG_HEART_BEAT, nullptr, 0);
    std::cout << "Heartbeat sent to control server" << std::endl;
  }
}
