#include "unix_socket.h"
#include <iostream>
#include <cstring>
#include <sys/epoll.h>
#include <errno.h>
#include <fcntl.h>
#include <mutex>
#include <algorithm>

// UnixSocketServer 实现
UnixSocketServer::UnixSocketServer(const std::string& socket_path)
    : socket_path_(socket_path), server_fd_(-1), running_(false),
      sequence_counter_(0) {
    memset(&current_client_addr_, 0, sizeof(current_client_addr_));
    current_client_len_ = 0;
}

UnixSocketServer::~UnixSocketServer() {
    stop();
}

bool UnixSocketServer::start() {
    if (running_.load()) {
        return true;
    }
    
    if (!setup_server_socket()) {
        return false;
    }
    
    // DGRAM套接字不需要epoll设置
    
    running_.store(true);

    // DGRAM套接字只需要消息处理线程
    process_thread_ = std::thread(&UnixSocketServer::process_loop, this);
    
    std::cout << "Unix Socket Server started on: " << socket_path_ << std::endl;
    return true;
}

void UnixSocketServer::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);

    // 等待消息处理线程结束
    if (process_thread_.joinable()) {
        process_thread_.join();
    }
    
    cleanup();
    std::cout << "Unix Socket Server stopped" << std::endl;
}

void UnixSocketServer::set_message_handler(MessageHandler handler) {
    message_handler_ = handler;
}

bool UnixSocketServer::send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) {
    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(client_fd, header, data);
}

void UnixSocketServer::broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    // DGRAM套接字的广播：发送到最后一个已知的客户端地址
    // 如果没有客户端地址，则忽略广播
    if (current_client_len_ > 0) {
        ipc_msg_hdr header;
        header.proto_version = 10;  // v1.0
        header.msg_type = msg_type;
        header.msg_len = length;
        header.transaction_id = sequence_counter_.fetch_add(1);

        // 发送消息头
        ssize_t sent = sendto(server_fd_, &header, sizeof(header), 0,
                             (struct sockaddr*)&current_client_addr_, current_client_len_);
        if (sent == sizeof(header) && data && length > 0) {
            // 发送消息体
            sendto(server_fd_, data, length, 0,
                  (struct sockaddr*)&current_client_addr_, current_client_len_);
        }
    }
}

size_t UnixSocketServer::get_client_count() const {
    // DGRAM套接字没有持久连接，返回是否有已知客户端地址
    return (current_client_len_ > 0) ? 1 : 0;
}

bool UnixSocketServer::setup_server_socket() {
    // 删除已存在的socket文件
    unlink(socket_path_.c_str());
    
    // 创建socket
    server_fd_ = socket(AF_UNIX, SOCK_DGRAM, 0);
    if (server_fd_ == -1) {
        std::cerr << "Failed to create socket: " << strerror(errno) << std::endl;
        return false;
    }
    
    // 设置socket地址
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);
    
    // 绑定socket
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        std::cerr << "Failed to bind socket: " << strerror(errno) << std::endl;
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }
    
    // DGRAM套接字不需要listen，只需要bind即可
    
    return true;
}

void UnixSocketServer::cleanup() {
    // 关闭服务器socket
    if (server_fd_ != -1) {
        close(server_fd_);
        server_fd_ = -1;
    }

    // 删除socket文件
    unlink(socket_path_.c_str());
}

// DGRAM套接字不需要客户端连接管理

// DGRAM套接字不需要accept_loop

void UnixSocketServer::process_loop() {
    uint8_t buffer[MAX_MESSAGE_SIZE];
    struct sockaddr_un client_addr;
    socklen_t client_len;

    while (running_.load()) {
        client_len = sizeof(client_addr);

        // 使用recvfrom接收数据报，设置超时
        struct timeval timeout;
        timeout.tv_sec = 1;  // 1秒超时
        timeout.tv_usec = 0;

        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(server_fd_, &readfds);

        int result = select(server_fd_ + 1, &readfds, NULL, NULL, &timeout);
        if (result == -1) {
            if (errno != EINTR && running_.load()) {
                std::cerr << "select failed: " << strerror(errno) << std::endl;
            }
            continue;
        } else if (result == 0) {
            // 超时，继续循环
            continue;
        }

        if (FD_ISSET(server_fd_, &readfds)) {
            ssize_t bytes_read = recvfrom(server_fd_, buffer, sizeof(buffer), 0,
                                        (struct sockaddr*)&client_addr, &client_len);

            if (bytes_read > 0) {
                // 解析消息，使用server_fd_作为虚拟客户端ID
                parse_message(server_fd_, buffer, bytes_read);

                // 记录客户端地址用于回复
                current_client_addr_ = client_addr;
                current_client_len_ = client_len;
            } else if (bytes_read == -1 && errno != EAGAIN && errno != EWOULDBLOCK) {
                std::cerr << "recvfrom failed: " << strerror(errno) << std::endl;
            }
        }
    }
}

// DGRAM套接字不需要单独的receive_message函数

bool UnixSocketServer::parse_message(int client_fd, const uint8_t* buffer, size_t length) {
    if (length < sizeof(ipc_msg_hdr)) {
        std::cerr << "Message too short" << std::endl;
        return false;
    }

    const ipc_msg_hdr* header = reinterpret_cast<const ipc_msg_hdr*>(buffer);

    if (header->proto_version != 10) {
        std::cerr << "Invalid protocol version" << std::endl;
        return false;
    }

    if (header->msg_len > MAX_MESSAGE_SIZE - sizeof(ipc_msg_hdr)) {
        std::cerr << "Message too large" << std::endl;
        return false;
    }

    if (length < sizeof(ipc_msg_hdr) + header->msg_len) {
        std::cerr << "Incomplete message" << std::endl;
        return false;
    }

    const uint8_t* data = buffer + sizeof(ipc_msg_hdr);

    if (message_handler_) {
        message_handler_(header->msg_type, data, header->msg_len, client_fd);
    }

    return true;
}

bool UnixSocketServer::send_message_internal(int client_fd, const ipc_msg_hdr& header, const uint8_t* data) {
    // 发送消息头
    ssize_t sent = send(client_fd, &header, sizeof(header), MSG_NOSIGNAL);
    if (sent != sizeof(header)) {
        return false;
    }

    // 发送消息体
    if (header.msg_len > 0 && data) {
        sent = send(client_fd, data, header.msg_len, MSG_NOSIGNAL);
        if (sent != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    }

    return true;
}

// UnixSocketClient 实现
UnixSocketClient::UnixSocketClient(const std::string& socket_path)
    : socket_path_(socket_path), client_fd_(-1), connected_(false),
      receiving_(false), sequence_counter_(0) {
}

UnixSocketClient::~UnixSocketClient() {
    disconnect();
}

bool UnixSocketClient::connect() {
    if (connected_.load()) {
        return true;
    }

    // 创建socket
    client_fd_ = socket(AF_UNIX, SOCK_DGRAM, 0);
    if (client_fd_ == -1) {
        std::cerr << "Failed to create client socket: " << strerror(errno) << std::endl;
        return false;
    }

    // 设置服务器地址（DGRAM不需要connect，只需要保存地址）
    memset(&server_addr_, 0, sizeof(server_addr_));
    server_addr_.sun_family = AF_UNIX;
    strncpy(server_addr_.sun_path, socket_path_.c_str(), sizeof(server_addr_.sun_path) - 1);

    connected_.store(true);
    std::cout << "DGRAM client ready for server: " << socket_path_ << std::endl;
    return true;
}

void UnixSocketClient::disconnect() {
    if (!connected_.load()) {
        return;
    }

    stop_async_receive();

    connected_.store(false);

    if (client_fd_ != -1) {
        close(client_fd_);
        client_fd_ = -1;
    }

    std::cout << "Disconnected from Unix Socket Server" << std::endl;
}

bool UnixSocketClient::send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(header, data);
}

bool UnixSocketClient::receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    if (!receive_message_internal(header, data)) {
        return false;
    }

    msg_type = header.msg_type;
    return true;
}

void UnixSocketClient::set_message_handler(MessageHandler handler) {
    message_handler_ = handler;
}

void UnixSocketClient::start_async_receive() {
    if (receiving_.load() || !connected_.load()) {
        return;
    }

    receiving_.store(true);
    receive_thread_ = std::thread(&UnixSocketClient::receive_loop, this);
}

void UnixSocketClient::stop_async_receive() {
    if (!receiving_.load()) {
        return;
    }

    receiving_.store(false);

    if (receive_thread_.joinable()) {
        receive_thread_.join();
    }
}

void UnixSocketClient::receive_loop() {
    while (receiving_.load() && connected_.load()) {
        uint32_t msg_type;
        std::vector<uint8_t> data;

        if (receive_message(msg_type, data, 1000)) { // 1秒超时
            if (message_handler_) {
                message_handler_(msg_type, data.data(), data.size(), client_fd_);
            }
        }
    }
}

bool UnixSocketClient::send_message_internal(const ipc_msg_hdr& header, const uint8_t* data) {
    // 发送消息头
    ssize_t sent = sendto(client_fd_, &header, sizeof(header), 0,
                         (struct sockaddr*)&server_addr_, sizeof(server_addr_));
    if (sent != sizeof(header)) {
        return false;
    }

    // 发送消息体
    if (header.msg_len > 0 && data) {
        sent = sendto(client_fd_, data, header.msg_len, 0,
                     (struct sockaddr*)&server_addr_, sizeof(server_addr_));
        if (sent != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    }

    return true;
}

bool UnixSocketClient::receive_message_internal(ipc_msg_hdr& header, std::vector<uint8_t>& data) {
    struct sockaddr_un from_addr;
    socklen_t from_len = sizeof(from_addr);

    // 接收消息头
    ssize_t received = recvfrom(client_fd_, &header, sizeof(header), 0,
                               (struct sockaddr*)&from_addr, &from_len);
    if (received != sizeof(header)) {
        return false;
    }

    if (header.proto_version != 10) {
        std::cerr << "Invalid protocol version in received message" << std::endl;
        return false;
    }

    if (header.msg_len > MAX_MESSAGE_SIZE) {
        std::cerr << "Message too large: " << header.msg_len << std::endl;
        return false;
    }

    // 接收消息体
    if (header.msg_len > 0) {
        data.resize(header.msg_len);
        received = recvfrom(client_fd_, data.data(), header.msg_len, 0,
                           (struct sockaddr*)&from_addr, &from_len);
        if (received != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    } else {
        data.clear();
    }

    return true;
}
