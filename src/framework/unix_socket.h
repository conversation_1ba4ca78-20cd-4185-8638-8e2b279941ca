#ifndef __UNIX_SOCKET_H__
#define __UNIX_SOCKET_H__

#include <string>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <vector>
#include <cstdint>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>
#include "ipc/ipc_msg.h"

// 消息处理回调函数类型
typedef std::function<void(uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd)> MessageHandler;



class UnixSocketServer {
public:
    UnixSocketServer(const std::string& socket_path);
    ~UnixSocketServer();

    // 启动服务器
    bool start();
    
    // 停止服务器
    void stop();
    
    // 设置消息处理回调
    void set_message_handler(MessageHandler handler);
    
    // 发送消息到指定客户端
    bool send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length);
    
    // 广播消息到所有客户端
    void broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length);
    
    // 获取连接的客户端数量
    size_t get_client_count() const;

private:
    std::string socket_path_;
    int server_fd_;
    std::atomic<bool> running_;
    std::thread process_thread_;
    MessageHandler message_handler_;

    // DGRAM套接字客户端地址管理
    struct sockaddr_un current_client_addr_;
    socklen_t current_client_len_;
    
    // 序列号生成
    std::atomic<uint32_t> sequence_counter_;
    
    // 内部方法
    void process_loop();
    bool setup_server_socket();
    void cleanup();
    bool send_message_internal(int client_fd, const ipc_msg_hdr& header, const uint8_t* data);
    
    // 消息处理
    bool parse_message(int client_fd, const uint8_t* buffer, size_t length);
    
    // 常量定义
    static const size_t MAX_MESSAGE_SIZE = 256 * 1024; // 优化：减少到256KB
};

class UnixSocketClient {
public:
    UnixSocketClient(const std::string& socket_path);
    ~UnixSocketClient();
    
    // 连接到服务器
    bool connect();
    
    // 断开连接
    void disconnect();
    
    // 发送消息
    bool send_message(uint32_t msg_type, const uint8_t* data, uint32_t length);
    
    // 接收消息（阻塞）
    bool receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms = 0);
    
    // 设置消息处理回调（异步接收）
    void set_message_handler(MessageHandler handler);
    
    // 启动异步接收
    void start_async_receive();
    
    // 停止异步接收
    void stop_async_receive();

private:
    std::string socket_path_;
    int client_fd_;
    std::atomic<bool> connected_;
    std::atomic<bool> receiving_;
    std::thread receive_thread_;
    MessageHandler message_handler_;

    // DGRAM套接字服务器地址
    struct sockaddr_un server_addr_;
    
    // 序列号生成
    std::atomic<uint32_t> sequence_counter_;
    
    // 内部方法
    void receive_loop();
    bool send_message_internal(const ipc_msg_hdr& header, const uint8_t* data);
    bool receive_message_internal(ipc_msg_hdr& header, std::vector<uint8_t>& data);
    
    // 常量定义
    static const size_t MAX_MESSAGE_SIZE = 256 * 1024; // 优化：减少到256KB
};

#endif // __UNIX_SOCKET_H__
