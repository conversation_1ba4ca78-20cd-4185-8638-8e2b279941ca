MODE = 1                                        # 0: offline pcap, 1: capture on interface
IF = ens33                                     # 捕获网卡名
PCAP_FILES_DIR = /root/pcaps/                   # pcap files dir
#RTXDR_CAP_FILTER = "host ***************"      #过滤器
ARM_DEVICE = 0                                  #1 = arm64 ;0 = amd64
RTP_TRANSFER_MODE = 0                          #1 = 原样转发 ; 0 = 解析nalu转发
LOG_LEVEL = 3                         #log等级 0 INFO 1 Warning 2 Error 3 Debug
HEARTBEAT_INTERVAL = 5                         # 心跳间隔(秒), 默认5秒
DUMP_PATH = '/root/dump/'
DUMP_SIZE = 1024
brand = "TEST"
serial = "001"