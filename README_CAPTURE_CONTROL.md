# 抓包控制功能使用说明

## 功能概述

本项目实现了通过Unix Socket进行抓包控制的功能，包括：

1. **主程序 (yaslc)**: 
   - 创建NALU服务端socket (`/run/nalu.sock`) 用于发送NALU帧数据
   - 创建控制客户端socket (`/run/ctrl.sock`) 用于接收抓包控制命令
   - 只有在抓包状态下才会发送NALU数据

2. **测试程序 (test_capture_control)**:
   - 创建控制服务端socket (`/run/ctrl.sock`) 用于发送抓包控制命令
   - 创建NALU客户端socket (`/run/nalu.sock`) 用于接收NALU数据
   - 将接收到的NALU数据写入 `recv.h264` 文件
   - 通过键盘输入控制抓包的开始和停止

## 编译

```bash
cd /home/<USER>/libipc
mkdir -p build
cd build
cmake ..
make yaslc test_capture_control -j4
```

## 使用方法

### 1. 启动主程序

```bash
cd /home/<USER>/libipc/run
sudo ./yaslc
```

主程序会：
- 启动NALU服务端socket在 `/run/nalu.sock`
- 启动控制客户端，尝试连接到 `/run/ctrl.sock`
- 初始状态为停止抓包

### 2. 启动测试程序

在另一个终端中：

```bash
cd /home/<USER>/libipc/run
sudo ./test_capture_control
```

测试程序会：
- 启动控制服务端socket在 `/run/ctrl.sock`
- 连接到NALU服务端socket `/run/nalu.sock`
- 显示控制命令提示

### 3. 控制抓包

在测试程序的终端中输入：

- `s` 或 `S`: 开始抓包
- `t` 或 `T`: 停止抓包  
- `q` 或 `Q`: 退出程序

### 4. 查看结果

抓包过程中，NALU数据会被写入到 `recv.h264` 文件中。可以使用视频播放器查看：

```bash
ffplay recv.h264
```

## Socket路径配置

默认socket路径：
- NALU服务端: `/run/nalu.sock`
- 控制socket: `/run/ctrl.sock`

可以通过命令行参数修改测试程序的socket路径：

```bash
sudo ./test_capture_control --control /tmp/ctrl.sock --nalu /tmp/nalu.sock
```

## 消息协议

### 控制消息 (MSG_TYPE_CONTROL)
- 数据长度: 1字节
- `0x01`: 开始抓包
- `0x00`: 停止抓包

### NALU数据消息 (MSG_TYPE_NALU_DATA)
- 数据内容: 原始NALU数据
- 写入文件时会自动添加H.264起始码 `00 00 00 01`

### 状态消息 (MSG_TYPE_STATUS)
- 数据长度: 1字节
- `0x01`: 抓包已开始
- `0x00`: 抓包已停止

## 注意事项

1. 需要root权限运行，因为socket路径在 `/run/` 目录下
2. 确保 `/run/` 目录存在且有写权限
3. 如果socket文件已存在，程序会自动清理
4. 主程序和测试程序的启动顺序可以任意，会自动重连
5. 测试程序退出时会自动清理socket文件

## 故障排除

### 1. 权限错误
```bash
sudo chown root:root /run/
sudo chmod 755 /run/
```

### 2. Socket连接失败
检查socket文件是否存在：
```bash
ls -la /run/*.sock
```

### 3. 编译错误
确保所有依赖库已安装：
```bash
sudo apt-get install libpcap-dev libglib2.0-dev
```

## 开发说明

### 主要修改的文件

1. `src/slc_capture.h` - 添加了抓包控制和socket相关的方法声明
2. `src/slc_capture.cpp` - 实现了抓包控制逻辑和socket通信
3. `src/slc_main.cpp` - 修改构造函数调用，添加新的socket路径参数
4. `src/test_capture_control.cpp` - 新增的测试程序
5. `src/CMakeLists.txt` - 添加测试程序的编译配置

### 关键功能

- **抓包状态控制**: 通过 `is_capturing_` 原子变量控制
- **NALU数据过滤**: 只有在抓包状态下才发送NALU数据
- **Socket通信**: 使用现有的UnixSocket框架进行通信
- **文件写入**: 自动添加H.264起始码并写入文件
