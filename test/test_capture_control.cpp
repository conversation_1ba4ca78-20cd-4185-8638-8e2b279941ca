#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <fstream>
#include <signal.h>
#include <unistd.h>
#include "../src/framework/unix_socket.h"
#include "../src/xop/RtspServer.h"
#include "../src/net/Timer.h"
class CaptureControlTest {
private:
    UnixSocketServer* control_server_;
    UnixSocketClient* nalu_client_;
    std::string control_socket_path_;
    std::string nalu_socket_path_;
    std::ofstream h264_file_;
    std::atomic<bool> running_;
    std::thread input_thread_;
    
public:
    CaptureControlTest(const std::string& control_path = "/run/ctrl.sock", 
                      const std::string& nalu_path = "/run/nalu.sock")
        : control_server_(nullptr), nalu_client_(nullptr),
          control_socket_path_(control_path), nalu_socket_path_(nalu_path),
          running_(true) {
    }
    
    ~CaptureControlTest() {
        stop();
    }
    
    bool start() {

        // 启动rtspServer
        std::string suffix = "live";
        std::string ip = "127.0.0.1";
        std::string port = "554";
        std::string rtsp_url = "rtsp://" + ip + ":" + port + "/" + suffix;

        std::shared_ptr<xop::EventLoop>  event_loop(new xop::EventLoop());
        std::shared_ptr<xop::RtspServer> server = xop::RtspServer::Create(event_loop.get());

        if (!server->Start("0.0.0.0", atoi(port.c_str()))) {
          printf("RTSP Server listen on %s failed.\n", port.c_str());
          return 0;
        }

      #ifdef AUTH_CONFIG
        server->SetAuthConfig("-_-", "admin", "12345");
      #endif

        xop::MediaSession *session = xop::MediaSession::CreateNew("live");
        session->AddSource(xop::channel_0, xop::H264Source::CreateNew());
        //session->StartMulticast();
        session->AddNotifyConnectedCallback([](xop::MediaSessionId sessionId, std::string peer_ip, uint16_t peer_port) {
          printf("RTSP client connect, ip=%s, port=%hu \n", peer_ip.c_str(), peer_port);
        });

        session->AddNotifyDisconnectedCallback([](xop::MediaSessionId sessionId, std::string peer_ip, uint16_t peer_port) {
          printf("RTSP client disconnect, ip=%s, port=%hu \n", peer_ip.c_str(), peer_port);
        });

        xop::MediaSessionId session_id = server->AddSession(session);
        g_cap->setRtspSession(server.get(), session_id);

        std::cout << "Play URL: " << rtsp_url << std::endl;

        // 打开H264输出文件
        h264_file_.open("recv.h264", std::ios::binary);
        if (!h264_file_.is_open()) {
            std::cerr << "Failed to open recv.h264 for writing" << std::endl;
            return false;
        }
        
        // 启动控制服务器
        if (!startControlServer()) {
            return false;
        }
        std::cout<<"wait for yaslc start"<<std::endl;
        sleep(10);
        // 连接到NALU客户端
        if (!connectNaluClient()) {
            return false;
        }
        
        // 启动输入处理线程
        input_thread_ = std::thread(&CaptureControlTest::inputLoop, this);
        
        std::cout << "Test program started successfully!" << std::endl;
        std::cout << "Commands:" << std::endl;
        std::cout << "  s - Start capture" << std::endl;
        std::cout << "  t - Stop capture" << std::endl;
        std::cout << "  q - Quit" << std::endl;
        
        return true;
    }
    
    void stop() {
        running_ = false;

        if (input_thread_.joinable()) {
            input_thread_.join();
        }

        if (control_server_) {
            control_server_->stop();
            delete control_server_;
            control_server_ = nullptr;
        }

        if (nalu_client_) {
            nalu_client_->stop_async_receive();
            nalu_client_->disconnect();
            delete nalu_client_;
            nalu_client_ = nullptr;
        }

        if (h264_file_.is_open()) {
            h264_file_.close();
        }

        std::cout << "Test program stopped" << std::endl;
    }

    bool isRunning() const {
        return running_;
    }
    
private:
    bool startControlServer() {
        control_server_ = new UnixSocketServer(control_socket_path_);
        
        // 设置消息处理回调
        control_server_->set_message_handler([this](uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd) {
            switch (msg_type) {
                case MSG_TYPE_STATUS:
                    if (length >= 1) {
                        uint8_t status = data[0];
                        std::cout << "Received capture status: " << (status ? "Started" : "Stopped") << std::endl;
                    }
                    break;
                case MSG_TYPE_HEARTBEAT:
                    // 回复心跳
                    control_server_->send_message(client_fd, MSG_TYPE_HEARTBEAT, nullptr, 0);
                    break;
                default:
                    std::cout << "Control server received unknown message type: " << msg_type << std::endl;
                    break;
            }
        });
        
        if (!control_server_->start()) {
            std::cerr << "Failed to start control server on " << control_socket_path_ << std::endl;
            delete control_server_;
            control_server_ = nullptr;
            return false;
        }
        
        std::cout << "Control server started on " << control_socket_path_ << std::endl;
        return true;
    }
    
    bool connectNaluClient() {
        nalu_client_ = new UnixSocketClient(nalu_socket_path_);
        
        // 设置消息处理回调
        nalu_client_->set_message_handler([this](uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd) {
            switch (msg_type) {
                case MSG_TYPE_NALU_DATA:
                    if (data && length > 0) {
                        // 写入H264起始码
                        const uint8_t start_code[] = {0x00, 0x00, 0x00, 0x01};
                        h264_file_.write(reinterpret_cast<const char*>(start_code), 4);
                        
                        // 写入NALU数据
                        h264_file_.write(reinterpret_cast<const char*>(data), length);
                        h264_file_.flush();
                        
                        std::cout << "Received NALU data: " << length << " bytes" << std::endl;
                        xop::AVFrame videoFrame = {0};
                        videoFrame.type = 0;
                        videoFrame.size = nalu_len;
                        videoFrame.timestamp = xop::H264Source::GetTimestamp();
                        videoFrame.buffer.reset(new uint8_t[videoFrame.size]);
                        memcpy(videoFrame.buffer.get(), nalu_head, videoFrame.size);
                        rtsp_server_->PushFrame(0, xop::channel_0, videoFrame);
                    }
                    break;
                case MSG_TYPE_HEARTBEAT:
                    // 心跳消息，不需要特殊处理
                    break;
                default:
                    std::cout << "NALU client received unknown message type: " << msg_type << std::endl;
                    break;
            }
        });
        
        // 尝试连接
        if (!nalu_client_->connect()) {
            std::cerr << "Failed to connect to NALU server on " << nalu_socket_path_ << std::endl;
            delete nalu_client_;
            nalu_client_ = nullptr;
            return false;
        }
        
        // 启动异步接收
        nalu_client_->start_async_receive();
        
        std::cout << "Connected to NALU server on " << nalu_socket_path_ << std::endl;
        return true;
    }
    
    void inputLoop() {
        char input;
        while (running_) {
            std::cout << "> ";
            std::cin >> input;
            
            switch (input) {
                case 's':
                case 'S':
                    sendCaptureCommand(true);
                    break;
                case 't':
                case 'T':
                    sendCaptureCommand(false);
                    break;
                case 'q':
                case 'Q':
                    running_ = false;
                    break;
                default:
                    std::cout << "Unknown command. Use 's' to start, 't' to stop, 'q' to quit." << std::endl;
                    break;
            }
        }
    }
    
    void sendCaptureCommand(bool start_capture) {
        if (!control_server_) {
            std::cerr << "Control server not available" << std::endl;
            return;
        }
        
        uint8_t command = start_capture ? 1 : 0;
        control_server_->broadcast_message(MSG_TYPE_CONTROL, &command, 1);
        
        std::cout << "Sent " << (start_capture ? "start" : "stop") << " capture command" << std::endl;
    }
};

// 全局变量用于信号处理
CaptureControlTest* g_test = nullptr;

void silgnalHandler(int signum) {
  std::cout << "\nReceived signal " << signum << ", shutting down..." << std::endl;
  if (g_test) {
    g_test->stop();
  }
  exit(0);
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    std::string control_path = "/run/ctrl.sock";
    std::string nalu_path = "/run/nalu.sock";
    
    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--control") == 0 && i + 1 < argc) {
            control_path = argv[i + 1];
            i++;
        } else if (strcmp(argv[i], "--nalu") == 0 && i + 1 < argc) {
            nalu_path = argv[i + 1];
            i++;
        } else if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) {
            std::cout << "Usage: " << argv[0] << " [OPTIONS]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  --control <path>  Control socket path (default: /run/ctrl.sock)" << std::endl;
            std::cout << "  --nalu <path>     NALU socket path (default: /run/nalu.sock)" << std::endl;
            std::cout << "  --help, -h        Show this help" << std::endl;
            return 0;
        }
    }
    
    // 创建测试实例
    g_test = new CaptureControlTest(control_path, nalu_path);
    
    if (!g_test->start()) {
        std::cerr << "Failed to start test program" << std::endl;
        delete g_test;
        return 1;
    }
    
    // 等待程序结束
    while (g_test) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        if (!g_test->isRunning()) {
            break;
        }
    }
    
    delete g_test;
    g_test = nullptr;
    
    return 0;
}
